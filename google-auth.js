/**
 * Google Authentication Service for CalenTask Chrome Extension
 * Handles OAuth 2.0 authentication with Google APIs
 */
class GoogleAuthService {
  constructor() {
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;
    this.isInitialized = false;
    this.initializationPromise = null;

    // Initialize authentication state from storage
    this.initializationPromise = this.loadAuthState();
  }

  /**
   * Load authentication state from Chrome storage
   */
  async loadAuthState() {
    try {
      console.log('Loading authentication state from storage...');
      const result = await chrome.storage.local.get([
        'google_access_token',
        'google_refresh_token',
        'google_token_expiry',
        'google_user_info',
        'google_auth_enabled'
      ]);

      this.accessToken = result.google_access_token || null;
      this.refreshToken = result.google_refresh_token || null;
      this.tokenExpiry = result.google_token_expiry || null;
      this.userInfo = result.google_user_info || null;
      this.isAuthenticated = result.google_auth_enabled || false;

      console.log('Loaded auth state:', {
        hasToken: !!this.accessToken,
        isAuthenticated: this.isAuthenticated,
        tokenExpiry: this.tokenExpiry,
        hasUserInfo: !!this.userInfo
      });

      // Check if we have a token but it's expired
      if (this.accessToken && this.isTokenExpired()) {
        console.log('Token is expired, attempting to refresh...');
        const refreshed = await this.refreshAccessToken();
        if (!refreshed) {
          console.log('Token refresh failed, clearing auth state');
          await this.clearAuthState();
        }
      } else if (this.accessToken && this.isAuthenticated) {
        // Verify the token is still valid by making a test request
        console.log('Verifying existing token...');
        const isValid = await this.verifyToken();
        if (!isValid) {
          console.log('Token verification failed, clearing auth state');
          await this.clearAuthState();
        }
      }

      this.isInitialized = true;
      console.log('Authentication initialization complete. Final state:', {
        isAuthenticated: this.isAuthenticated,
        hasValidToken: !!this.accessToken && !this.isTokenExpired()
      });
    } catch (error) {
      console.error('Error loading auth state:', error);
      await this.clearAuthState();
      this.isInitialized = true;
    }
  }

  /**
   * Wait for authentication initialization to complete
   */
  async waitForInitialization() {
    if (this.isInitialized) {
      return;
    }
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  /**
   * Verify that the current token is still valid
   */
  async verifyToken() {
    if (!this.accessToken) {
      return false;
    }

    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        // Update user info if the token is valid
        const userInfo = await response.json();
        if (userInfo) {
          this.userInfo = userInfo;
          await this.saveAuthState();
        }
        return true;
      } else {
        console.log('Token verification failed with status:', response.status);
        return false;
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      return false;
    }
  }

  /**
   * Save authentication state to Chrome storage
   */
  async saveAuthState() {
    try {
      await chrome.storage.local.set({
        google_access_token: this.accessToken,
        google_refresh_token: this.refreshToken,
        google_token_expiry: this.tokenExpiry,
        google_user_info: this.userInfo,
        google_auth_enabled: this.isAuthenticated
      });
      console.log('Authentication state saved to storage');
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  }

  /**
   * Clear authentication state
   */
  async clearAuthState() {
    console.log('Clearing authentication state');
    this.isAuthenticated = false;
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiry = null;
    this.userInfo = null;

    try {
      await chrome.storage.local.remove([
        'google_access_token',
        'google_refresh_token',
        'google_token_expiry',
        'google_user_info',
        'google_auth_enabled'
      ]);
      console.log('Authentication state cleared from storage');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  /**
   * Check if the current access token is expired
   */
  isTokenExpired() {
    if (!this.tokenExpiry) return true;
    return Date.now() >= this.tokenExpiry;
  }

  /**
   * Sign in with Google using Chrome Identity API
   */
  async signIn() {
    try {
      // Use Chrome Identity API's launchWebAuthFlow method
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com'; // Your client ID
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';

      // Build the authorization URL
      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);

      console.log('Using auth URL:', authURL.toString());
      console.log('Redirect URL:', redirectURL);

      // Launch the web auth flow
      const responseURL = await chrome.identity.launchWebAuthFlow({
        url: authURL.toString(),
        interactive: true
      });

      if (responseURL) {
        // Extract access token from the response URL
        const url = new URL(responseURL);
        const params = new URLSearchParams(url.hash.substring(1));
        this.accessToken = params.get('access_token');
        const expiresIn = params.get('expires_in');

        if (this.accessToken) {
          this.isAuthenticated = true;
          this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);

          // Get user info
          await this.fetchUserInfo();

          // Save state
          await this.saveAuthState();

          console.log('Google sign-in successful');
          return true;
        }
      }
    } catch (error) {
      console.error('Google sign-in failed:', error);
      this.clearAuthState();
      throw new Error(`Authentication failed: ${error.message}`);
    }

    return false;
  }

  /**
   * Sign out from Google
   */
  async signOut() {
    try {
      if (this.accessToken) {
        // Revoke the token
        await chrome.identity.removeCachedAuthToken({ token: this.accessToken });
      }

      await this.clearAuthState();
      console.log('Google sign-out successful');
      return true;
    } catch (error) {
      console.error('Google sign-out failed:', error);
      // Clear state anyway
      await this.clearAuthState();
      return false;
    }
  }

  /**
   * Refresh the access token
   */
  async refreshAccessToken() {
    try {
      // For the launchWebAuthFlow approach, we need to re-authenticate
      // as refresh tokens aren't typically used with this flow
      const clientId = '*************-35d9q9r35e0iqf859419gtqa63ikmm8c.apps.googleusercontent.com';
      const redirectURL = chrome.identity.getRedirectURL();
      const scopes = 'https://www.googleapis.com/auth/calendar.readonly';

      const authURL = new URL('https://accounts.google.com/o/oauth2/auth');
      authURL.searchParams.set('client_id', clientId);
      authURL.searchParams.set('response_type', 'token');
      authURL.searchParams.set('redirect_uri', redirectURL);
      authURL.searchParams.set('scope', scopes);

      // Non-interactive refresh attempt
      try {
        const responseURL = await chrome.identity.launchWebAuthFlow({
          url: authURL.toString(),
          interactive: false
        });

        if (responseURL) {
          const url = new URL(responseURL);
          const params = new URLSearchParams(url.hash.substring(1));
          this.accessToken = params.get('access_token');
          const expiresIn = params.get('expires_in');

          if (this.accessToken) {
            this.tokenExpiry = Date.now() + (parseInt(expiresIn) * 1000);
            await this.saveAuthState();
            return true;
          }
        }
      } catch (nonInteractiveError) {
        console.log('Non-interactive token refresh failed, will try interactive');
        // Fall through to interactive auth
      }

      // If non-interactive refresh fails, try interactive
      return await this.signIn();
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearAuthState();
      return false;
    }
  }

  /**
   * Fetch user information from Google API
   */
  async fetchUserInfo() {
    if (!this.accessToken) return null;

    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      });

      if (response.ok) {
        this.userInfo = await response.json();
        return this.userInfo;
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
    }

    return null;
  }

  /**
   * Get a valid access token, refreshing if necessary
   */
  async getValidToken() {
    if (!this.isAuthenticated || !this.accessToken) {
      throw new Error('Not authenticated');
    }

    if (this.isTokenExpired()) {
      const refreshed = await this.refreshAccessToken();
      if (!refreshed) {
        throw new Error('Failed to refresh token');
      }
    }

    return this.accessToken;
  }

  /**
   * Make an authenticated API request to Google
   */
  async makeAuthenticatedRequest(url, options = {}) {
    const token = await this.getValidToken();

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    if (response.status === 401) {
      // Token might be invalid, try to refresh
      const refreshed = await this.refreshAccessToken();
      if (refreshed) {
        // Retry the request with new token
        const newToken = await this.getValidToken();
        headers['Authorization'] = `Bearer ${newToken}`;
        return fetch(url, { ...options, headers });
      } else {
        throw new Error('Authentication failed');
      }
    }

    return response;
  }

  /**
   * Get authentication status (synchronous, may not be fully initialized)
   */
  getAuthStatus() {
    return {
      isAuthenticated: this.isAuthenticated,
      userInfo: this.userInfo,
      tokenExpiry: this.tokenExpiry,
      isTokenExpired: this.isTokenExpired(),
      isInitialized: this.isInitialized
    };
  }

  /**
   * Get authentication status after waiting for initialization
   */
  async getAuthStatusAsync() {
    await this.waitForInitialization();
    return this.getAuthStatus();
  }
}

// Export for use in other modules
window.GoogleAuthService = GoogleAuthService;
